{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["onnx_model_path = \"./model/model.onnx\"\n", "\n", "ExecutionProvider=\"QNNExecutionProvider\"\n", "if ExecutionProvider == \"OpenVINOExecutionProvider\":\n", "    onnx_model_path = \"./model/openvino_model_st_quant.onnx\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = \"This is an example sentence.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import onnxruntime as ort\n", "import torch\n", "import torch.nn.functional as F\n", "\n", "from transformers import AutoModel, AutoTokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def mean_pooling(model_output, attention_mask):\n", "    token_embeddings = torch.tensor(model_output[0])\n", "    input_mask_expanded = attention_mask.unsqueeze(-1).expand_as(token_embeddings).float()\n", "    return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained('google-bert/bert-base-multilingual-cased')\n", "encoded_input = tokenizer(\n", "    inputs,\n", "    padding=\"max_length\",\n", "    max_length=128,\n", "    truncation=True,\n", "    add_special_tokens=True,\n", "    return_tensors=\"pt\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def add_ep_for_device(session_options, ep_name, device_type, ep_options=None):\n", "    ep_devices = ort.get_ep_devices()\n", "    for ep_device in ep_devices:\n", "        if ep_device.ep_name == ep_name and ep_device.device.type == device_type:\n", "            print(f\"Adding {ep_name} for {device_type}\")\n", "            session_options.add_provider_for_devices([ep_device], {} if ep_options is None else ep_options)\n", "\n", "\n", "session_options = ort.SessionOptions()\n", "\n", "add_ep_for_device(session_options, ExecutionProvider, ort.OrtHardwareDeviceType.NPU)\n", "\n", "session = ort.InferenceSession(\n", "    onnx_model_path, # a model wirh QNN EPContext nodes\n", "    sess_options=session_options,\n", ")\n", "\n", "input_ids = encoded_input[\"input_ids\"]\n", "attention_mask = encoded_input[\"attention_mask\"]\n", "token_type_ids = encoded_input[\"token_type_ids\"]\n", "inputs = {\n", "    \"input_ids\": input_ids.long().cpu().numpy(),\n", "    \"attention_mask\": attention_mask.long().cpu().numpy(),\n", "    \"token_type_ids\": token_type_ids.long().cpu().numpy()\n", "}\n", "\n", "outputs = session.run(None, inputs)\n", "embeds_1 = mean_pooling(outputs, encoded_input['attention_mask'])\n", "embeds_1 = F.normalize(embeds_1, p=2, dim=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get text embedding from orinal model, as ground truth.\n", "model = AutoModel.from_pretrained('google-bert/bert-base-multilingual-cased').eval()\n", "with torch.no_grad():\n", "    outputs = model(**encoded_input)\n", "    embeds_2 = mean_pooling(outputs, encoded_input['attention_mask'])\n", "    embeds_2 = F.normalize(embeds_2, p=2, dim=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["similarity = F.cosine_similarity(embeds_1, embeds_2).item()\n", "print(\"Similarity: \", similarity)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}