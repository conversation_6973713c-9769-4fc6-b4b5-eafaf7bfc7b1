{"workflows": [{"name": "Convert to QNN", "file": "bert-base-multilingual-cased_qdq_qnn.json", "template": "huggingface/google-bert/bert-base-multilingual-cased", "version": 1, "templateName": "bert-base-multilingual-cased_qdq_qnn"}, {"name": "Convert to AMD NPU", "file": "bert-base-multilingual-cased_qdq_amd.json", "template": "huggingface/google-bert/bert-base-multilingual-cased", "version": 1, "templateName": "bert-base-multilingual-cased_qdq_amd"}, {"name": "Convert to Intel NPU", "file": "bert-base-multilingual-cased_context_ov_static.json", "template": "huggingface/google-bert/bert-base-multilingual-cased", "version": 1, "templateName": "bert-base-multilingual-cased_context_ov_static"}], "modelInfo": {"id": "huggingface/google-bert/bert-base-multilingual-cased"}}