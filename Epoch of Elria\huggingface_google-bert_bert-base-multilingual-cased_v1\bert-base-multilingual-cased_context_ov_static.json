{"input_model": {"type": "HfModel", "model_path": "google-bert/bert-base-multilingual-cased", "task": "fill-mask"}, "systems": {"local_system": {"type": "LocalSystem", "accelerators": [{"device": "npu", "execution_providers": ["OpenVINOExecutionProvider"]}]}}, "data_configs": [{"name": "quantize_data_config", "user_script": "user_script.py", "load_dataset_config": {"type": "bert_base_multilingual_cased_dataset", "data_name": "wikipedia", "split": "train", "max_samples": 300}, "dataloader_config": {"batch_size": 1, "drop_last": true}}], "evaluators": {"common_evaluator": {"metrics": [{"name": "latency", "type": "latency", "sub_types": [{"name": "avg", "priority": 1, "metric_config": {"warmup_num": 20, "repeat_test_num": 100}}, {"name": "p90", "metric_config": {"warmup_num": 20, "repeat_test_num": 100}}]}]}}, "passes": {"optimum_convert": {"type": "OpenVINOOptimumConversion", "extra_args": {"device": "npu", "task": "feature-extraction"}}, "io_update": {"type": "OpenVINOIoUpdate", "input_shapes": [[1, 128], [1, 128], [1, 128]], "static": true}, "ov_quantize": {"type": "OpenVINOQuantization", "target_device": "npu", "data_config": "quantize_data_config", "model_type": "TRANSFORMER", "user_script": "user_script.py", "transform_fn": "custom_transform_func"}, "encapsulation": {"type": "OpenVINOEncapsulation", "target_device": "npu", "ov_version": "2025.1"}}, "search_strategy": false, "host": "local_system", "target": "local_system", "cache_dir": "cache", "evaluator": "common_evaluator", "evaluate_input_model": false, "output_dir": "model/bert-base-multilingual-cased_context_ov_static"}